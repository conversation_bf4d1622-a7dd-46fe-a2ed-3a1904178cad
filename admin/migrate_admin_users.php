<?php
// 数据库迁移脚本：创建管理员用户表
include_once __DIR__ . '/../config/db_config.php';

// 检查表是否已存在
$check_table = "SHOW TABLES LIKE 'admin_users'";
$result = $mysqli->query($check_table);

if ($result->num_rows == 0) {
    // 创建admin_users表
    $create_table_sql = "
    CREATE TABLE `admin_users` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
      `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
      `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      UNIQUE KEY `username` (`username`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    if ($mysqli->query($create_table_sql) === TRUE) {
        echo "管理员用户表创建成功。<br>";
        
        // 插入现有的管理员账户
        $insert_admins_sql = "
        INSERT INTO `admin_users` (`username`, `password`) VALUES
        ('admin', '21232f297a57a5a743894a0e4a801fc3'),
        ('Michael', '21232f297a57a5a743894a0e4a801fc3');
        ";
        
        if ($mysqli->query($insert_admins_sql) === TRUE) {
            echo "管理员账户数据插入成功。<br>";
            echo "迁移完成！现在可以使用数据库存储的管理员账户了。<br>";
        } else {
            echo "插入管理员账户数据失败: " . $mysqli->error . "<br>";
        }
    } else {
        echo "创建管理员用户表失败: " . $mysqli->error . "<br>";
    }
} else {
    echo "管理员用户表已存在，无需创建。<br>";
}

$mysqli->close();
?>
