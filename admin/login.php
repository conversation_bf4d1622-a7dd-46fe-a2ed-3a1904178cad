<?php
session_start();
include_once '../config/db_config.php';

// 处理表单提交
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = $_POST['username'];
    $password = $_POST['password'];
    $captcha_input = $_POST['captcha'];

    // 检查是否因尝试次数过多而被锁定
    if (isset($_SESSION['login_lockout_time']) && time() < $_SESSION['login_lockout_time']) {
        $remaining_time = ceil(($_SESSION['login_lockout_time'] - time()) / 60);
        $_SESSION['login_attempts_exceeded_message'] = "登录尝试次数过多，请在 " . $remaining_time . " 分钟后重试。";
        header("Location: login.php");
        exit;
    } else {
        // 清除锁定相关的会话变量（如果冷却时间已过）
        unset($_SESSION['login_lockout_time']);
        unset($_SESSION['login_attempts_exceeded_message']);
    }

    // 验证验证码
    if (!isset($_SESSION['captcha']) || $captcha_input != $_SESSION['captcha']) {
        $_SESSION['login_error'] = "验证码不正确。";
        header("Location: login.php");
        exit;
    }
    unset($_SESSION['captcha']); // 验证码使用后立即销毁

    // 验证用户名和密码
    $stmt = $mysqli->prepare("SELECT id, username, password FROM admin_users WHERE username = ?");
    if ($stmt) {
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows == 1) {
            $admin = $result->fetch_assoc();
            if ($admin['password'] === md5($password)) {
                // 登录成功
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_username'] = $username;
                $_SESSION['admin_id'] = $admin['id'];
                unset($_SESSION['login_attempts']); // 清除登录尝试次数
                unset($_SESSION['login_error']);
                unset($_SESSION['login_attempts_exceeded_message']);
                $stmt->close();
                header("Location: dashboard.php"); // 跳转到后台主页
                exit;
            }
        }
        $stmt->close();
    }

    // 登录失败
    if (!isset($_SESSION['login_attempts'])) {
        $_SESSION['login_attempts'] = 0;
    }
    $_SESSION['login_attempts']++;

    if ($_SESSION['login_attempts'] >= 3) {
        $_SESSION['login_lockout_time'] = time() + (3 * 60); // 锁定3分钟
        $_SESSION['login_attempts_exceeded_message'] = "登录尝试次数过多，请在 3 分钟后重试。";
        unset($_SESSION['login_attempts']); // 重置尝试次数
        unset($_SESSION['login_error']);
    } else {
        $_SESSION['login_error'] = "用户名或密码不正确。您还有 " . (3 - $_SESSION['login_attempts']) . " 次尝试机会。";
    }
    header("Location: login.php");
    exit;
}

// 生成验证码
$captcha_num = rand(1000, 9999);
$_SESSION['captcha'] = $captcha_num;
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 问卷系统</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #e6f7ff; /* 淡蓝色背景 */
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .login-container {
            background-color: #ffffff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            width: 360px;
            text-align: center;
        }
        .login-container h2 {
            color: #0050b3; /* 深蓝色标题 */
            margin-bottom: 30px;
        }
        .login-container label {
            display: block;
            text-align: left;
            margin-bottom: 8px;
            color: #333;
            font-weight: bold;
        }
        .login-container input[type="text"],
        .login-container input[type="password"] {
            width: calc(100% - 22px);
            padding: 10px;
            margin-bottom: 20px;
            border: 1px solid #b3d9ff; /* 浅蓝色边框 */
            border-radius: 4px;
            box-sizing: border-box;
        }
        .login-container input[type="text"]:focus,
        .login-container input[type="password"]:focus {
            border-color: #007bff; /* 焦点时深蓝色边框 */
            outline: none;
        }
        .captcha-container {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        .captcha-container input[type="text"] {
            width: calc(60% - 12px); /* 调整宽度以适应验证码图像 */
            margin-bottom: 0; /* 移除底边距 */
            margin-right: 10px;
        }
        .captcha-image {
            width: 35%;
            height: 40px;
            line-height: 40px;
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            border-radius: 4px;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            user-select: none; /* 防止文本被选中 */
            color: #0050b3;
        }
        .login-container button {
            background-color: #007bff; /* 主蓝色按钮 */
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
            font-size: 16px;
            transition: background-color 0.3s ease;
        }
        .login-container button:hover {
            background-color: #0056b3; /* 悬停时深蓝色 */
        }
        .error-message {
            color: red;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2>管理员登录</h2>
        <form action="login.php" method="post">
            <label for="username">用户名:</label>
            <input type="text" id="username" name="username" required>

            <label for="password">密码:</label>
            <input type="password" id="password" name="password" required>

            <label for="captcha">验证码:</label>
            <div class="captcha-container">
                <input type="text" id="captcha" name="captcha" required maxlength="4">
                <div class="captcha-image"><?php echo $captcha_num; ?></div>
            </div>

            <button type="submit">登录</button>

            <?php
            if (isset($_SESSION['login_error'])) {
                echo '<p class="error-message">' . $_SESSION['login_error'] . '</p>';
                unset($_SESSION['login_error']); // 显示后清除错误信息
            }
            if (isset($_SESSION['login_attempts_exceeded_message'])) {
                echo '<p class="error-message">' . $_SESSION['login_attempts_exceeded_message'] . '</p>';
                // 此处不清除，直到冷却时间结束
            }
            ?>
        </form>
    </div>
</body>
</html>
