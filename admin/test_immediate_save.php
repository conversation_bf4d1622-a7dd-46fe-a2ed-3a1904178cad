<?php
// 测试立即保存功能
session_start();
include_once "../config/db_config.php";

// 模拟登录状态
$_SESSION["admin_logged_in"] = true;

echo "POST数据: ";
var_dump($_POST);

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    echo "收到POST请求\n";
    
    if (isset($_POST["update_question_immediate"])) {
        echo "找到update_question_immediate参数\n";
        
        $questionnaire_id = $_POST["questionnaire_id"] ?? "";
        $question_id = $_POST["question_id"] ?? "";
        
        echo "问卷ID: {$questionnaire_id}\n";
        echo "题目ID: {$question_id}\n";
        
        if (!empty($questionnaire_id) && !empty($question_id)) {
            echo "参数验证通过\n";
            
            // 简单的测试更新
            $test_label = "测试更新 - " . date("Y-m-d H:i:s");
            $stmt = $mysqli->prepare("UPDATE questions SET label = ? WHERE id = ? AND questionnaire_id = ?");
            if ($stmt) {
                $stmt->bind_param("sss", $test_label, $question_id, $questionnaire_id);
                if ($stmt->execute()) {
                    echo "✓ 数据库更新成功\n";
                    echo "新标题: {$test_label}\n";
                } else {
                    echo "✗ 数据库更新失败: " . $stmt->error . "\n";
                }
                $stmt->close();
            } else {
                echo "✗ 准备语句失败: " . $mysqli->error . "\n";
            }
        } else {
            echo "✗ 参数验证失败\n";
        }
    } else {
        echo "未找到update_question_immediate参数\n";
    }
} else {
    echo "未收到POST请求\n";
}
?>