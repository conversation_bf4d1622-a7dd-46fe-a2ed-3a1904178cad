<?php
// 确保此文件被 dashboard.php 正确包含
if (basename(__FILE__) == basename($_SERVER['SCRIPT_FILENAME'])) {
    echo "此页面不能直接访问。";
    exit;
}

// 检查是否有正在创建的问卷信息
if (!isset($_SESSION['current_questionnaire_id']) || !isset($_SESSION['current_questionnaire_title'])) {
    // 如果没有，则重定向到创建标题页面
    $_SESSION['message'] = ['type' => 'error', 'text' => '请先创建问卷标题。'];
    header("Location: dashboard.php?page=create_questionnaire_title");
    exit;
}

// global $mysqli; // 如果需要数据库操作

$questionnaire_id = $_SESSION['current_questionnaire_id'];
$questionnaire_title = $_SESSION['current_questionnaire_title'];

// 初始化问题数组（如果尚未存在）
if (!isset($_SESSION['current_questionnaire_questions'])) {
    $_SESSION['current_questionnaire_questions'] = [];
}
$questions = &$_SESSION['current_questionnaire_questions']; // 使用引用方便操作

$error_message = '';
$success_message = '';

// 处理添加问题的表单提交
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['add_question'])) {
    $question_type = $_POST['question_type'] ?? '';
    $question_label = trim($_POST['question_label'] ?? ''); // 通用标签/题目
    $options = $_POST['options'] ?? []; // 单选题选项

    if (empty($question_type)) {
        $error_message = "请选择一个题型。";
    } elseif (empty($question_label) && !in_array($question_type, ['name', 'id_card', 'phone'])) {
        // 对于姓名、身份证、手机，标签是固定的，可以不强制用户输入
        $error_message = "题目内容不能为空。";
    } else {
        $new_question = [
            'type' => $question_type,
            'label' => $question_label, // 用户输入的题目或默认标签
            'id' => 'q_' . uniqid() // 为每个问题生成唯一ID
        ];

        // 根据题型处理特定字段
        switch ($question_type) {
            case 'name':
                $new_question['label'] = !empty($question_label) ? $question_label : '姓名';
                break;
            case 'id_card':
                $new_question['label'] = !empty($question_label) ? $question_label : '身份证号码';
                break;
            case 'phone':
                $new_question['label'] = !empty($question_label) ? $question_label : '手机';
                break;
            case 'single_choice':
                $valid_options = array_filter(array_map('trim', $options));
                if (count($valid_options) < 2) {
                    $error_message = "单选题至少需要2个有效选项。";
                } else {
                    $new_question['options'] = $valid_options;
                }
                break;
            case 'fill_blank':
                // 标签已在通用部分处理
                break;
            default:
                $error_message = "无效的题型。";
                break;
        }

        if (empty($error_message)) {
            $questions[] = $new_question;
            $success_message = "问题已添加：" . htmlspecialchars($new_question['label']);
            // 清空表单或重置状态，以便添加下一个问题
            $_POST = []; // 简单清空，实际应用中可能需要更精细控制
            // 或者可以考虑重定向到自身以避免重复提交 (Post/Redirect/Get pattern)
            // header("Location: dashboard.php?page=create_questionnaire_questions&question_added=1");
            // exit;
        }
    }
}

// 处理删除问题的操作
if (isset($_GET['action']) && $_GET['action'] == 'delete_question' && isset($_GET['question_id'])) {
    $question_to_delete_id = $_GET['question_id'];
    foreach ($questions as $key => $question) {
        if ($question['id'] == $question_to_delete_id) {
            unset($questions[$key]);
            $_SESSION['message'] = ['type' => 'success', 'text' => '问题已删除。'];
            // 重置数组索引
            $questions = array_values($questions);
            header("Location: dashboard.php?page=create_questionnaire_questions");
            exit;
        }
    }
    $_SESSION['message'] = ['type' => 'error', 'text' => '未找到要删除的问题。'];
    header("Location: dashboard.php?page=create_questionnaire_questions");
    exit;
}

// 处理调整问题顺序的操作
if (isset($_GET['action']) && isset($_GET['question_id'])) {
    $question_id = $_GET['question_id'];
    $action = $_GET['action'];

    // 找到问题在数组中的位置
    $question_index = -1;
    foreach ($questions as $index => $question) {
        if ($question['id'] == $question_id) {
            $question_index = $index;
            break;
        }
    }

    if ($question_index !== -1) {
        if ($action == 'move_up' && $question_index > 0) {
            // 上移：与前一个问题交换位置
            $temp = $questions[$question_index];
            $questions[$question_index] = $questions[$question_index - 1];
            $questions[$question_index - 1] = $temp;
            $_SESSION['message'] = ['type' => 'success', 'text' => '问题已上移。'];
        } elseif ($action == 'move_down' && $question_index < count($questions) - 1) {
            // 下移：与后一个问题交换位置
            $temp = $questions[$question_index];
            $questions[$question_index] = $questions[$question_index + 1];
            $questions[$question_index + 1] = $temp;
            $_SESSION['message'] = ['type' => 'success', 'text' => '问题已下移。'];
        }
        header("Location: dashboard.php?page=create_questionnaire_questions");
        exit;
    }
}


// 处理保存整个问卷的逻辑
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['save_questionnaire'])) {
    if (empty($questions)) {
        $error_message = "问卷中还没有任何问题，请至少添加一个问题。";
    } else {
        // 在这里将 $questionnaire_id, $questionnaire_title, 和 $questions 数组保存到数据库
        // 1. 保存问卷基本信息 (questionnaires 表)
        // 2. 保存问卷的问题 (questions 表)

        global $mysqli; // 确保 $mysqli 在此作用域内可用

        // 开始事务
        $mysqli->begin_transaction();

        try {
            // 保存问卷基本信息
            $stmt = $mysqli->prepare("INSERT INTO questionnaires (id, title, created_at, status) VALUES (?, ?, NOW(), 'draft')");
            if (!$stmt) {
                throw new Exception("Prepare statement for questionnaires failed: " . $mysqli->error);
            }
            $stmt->bind_param("ss", $questionnaire_id, $questionnaire_title);
            if (!$stmt->execute()) {
                throw new Exception("Execute statement for questionnaires failed: " . $stmt->error);
            }
            $stmt->close();

            // 保存问卷的问题
            foreach ($questions as $index => $q_data) {
                $q_id = $q_data['id'];
                $q_type = $q_data['type'];
                $q_label = $q_data['label'];
                // 将选项数组转为JSON字符串存储，如果不存在则为NULL
                $q_options = (isset($q_data['options']) && is_array($q_data['options'])) ? json_encode($q_data['options'], JSON_UNESCAPED_UNICODE) : null;
                $q_order = $index + 1; // 问题顺序

                $stmt_q = $mysqli->prepare("INSERT INTO questions (id, questionnaire_id, type, label, options, sort_order, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
                if (!$stmt_q) {
                    throw new Exception("Prepare statement for questions failed: " . $mysqli->error);
                }
                $stmt_q->bind_param("sssssi", $q_id, $questionnaire_id, $q_type, $q_label, $q_options, $q_order);
                if (!$stmt_q->execute()) {
                    throw new Exception("Execute statement for questions failed: " . $stmt_q->error);
                }
                $stmt_q->close();
            }

            // 提交事务
            $mysqli->commit();

            // 清理 session 中的临时问卷数据
            unset($_SESSION['current_questionnaire_id']);
            unset($_SESSION['current_questionnaire_title']);
            unset($_SESSION['current_questionnaire_questions']);

            $_SESSION['message'] = ['type' => 'success', 'text' => "问卷已成功保存：" . htmlspecialchars($questionnaire_title) . "(ID: " . $questionnaire_id . ")"];
            header("Location: dashboard.php?page=all_questionnaires"); // 跳转到问卷列表
            exit;

        } catch (Exception $e) {
            // 如果发生错误，回滚事务
            $mysqli->rollback();
            $error_message = "保存问卷失败: " . $e->getMessage();
            // 不清除session，以便用户可以尝试修复并重新保存
        }
    }
}

// 显示会话消息
if (isset($_SESSION['message'])) {
    if ($_SESSION['message']['type'] == 'success') {
        $success_message = $_SESSION['message']['text'];
    } else {
        $error_message = $_SESSION['message']['text'];
    }
    unset($_SESSION['message']);
}

$current_selected_type = $_GET['type'] ?? ''; // 用于保持左侧选择题型后，右侧显示对应表单
?>
<style>
    .question-creator-container {
        display: flex;
        gap: 20px;
        min-height: 500px; /* 保证有足够高度 */
    }
    .question-types-sidebar {
        width: 200px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }
    .question-types-sidebar h3 {
        margin-top: 0;
        margin-bottom: 15px;
        font-size: 16px;
        color: #0050b3;
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 10px;
    }
    .question-types-sidebar ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    .question-types-sidebar li a {
        display: block;
        padding: 10px 12px;
        text-decoration: none;
        color: #007bff;
        border-radius: 4px;
        margin-bottom: 5px;
        transition: background-color 0.2s ease;
    }
    .question-types-sidebar li a:hover, .question-types-sidebar li a.active {
        background-color: #007bff;
        color: white;
    }

    .question-settings-main {
        flex-grow: 1;
        padding: 20px;
        background-color: #fff;
        border-radius: 6px;
        box-shadow: 0 1px 5px rgba(0,0,0,0.08);
    }
    .question-settings-main h1 {
         font-size: 20px; color: #0050b3; margin-bottom: 5px;
    }
    .question-settings-main .questionnaire-title-display {
        font-size: 14px; color: #555; margin-bottom:20px; font-weight:bold;
    }

    .form-section { margin-bottom: 20px; }
    .form-section label { display: block; margin-bottom: 6px; font-weight: bold; font-size: 14px; }
    .form-section input[type="text"], .form-section textarea {
        width: calc(100% - 20px);
        padding: 9px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        box-sizing: border-box;
    }
    .form-section input[type="text"]:focus, .form-section textarea:focus {
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }
    .options-container .option-item { display: flex; align-items: center; margin-bottom: 8px; }
    .options-container .option-item input[type="text"] { flex-grow: 1; margin-right: 8px; }
    .btn-add-option, .btn-remove-option, .btn-save-question, .btn-save-questionnaire {
        padding: 8px 15px;
        font-size: 14px;
        border-radius: 4px;
        cursor: pointer;
        border: 1px solid transparent;
        margin-right: 5px;
    }
    .btn-add-option { background-color: #28a745; color: white; border-color: #28a745; }
    .btn-add-option:hover { background-color: #218838; }
    .btn-remove-option { background-color: #dc3545; color: white; border-color: #dc3545; }
    .btn-remove-option:hover { background-color: #c82333; }
    .btn-save-question { background-color: #007bff; color: white; border-color: #007bff; }
    .btn-save-question:hover { background-color: #0056b3; }
    .btn-save-questionnaire { background-color: #17a2b8; color: white; border-color: #17a2b8; float: right; }
    .btn-save-questionnaire:hover { background-color: #138496; }


    .added-questions-list { margin-top: 30px; }
    .added-questions-list h3 { font-size: 18px; color: #0050b3; border-bottom: 1px solid #eee; padding-bottom: 8px; }
    .added-questions-list ul { list-style: none; padding: 0; }
    .added-questions-list li {
        background-color: #f8f9fa;
        padding: 12px 15px;
        margin-bottom: 10px;
        border-radius: 4px;
        border: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .added-questions-list li .question-info { flex-grow: 1; }
    .added-questions-list li .question-label { font-weight: bold; }
    .added-questions-list li .question-type-badge {
        font-size: 0.8em;
        padding: 3px 7px;
        background-color: #6c757d;
        color: white;
        border-radius: 3px;
        margin-left: 8px;
    }
    .added-questions-list li .actions a {
        color: #dc3545;
        text-decoration: none;
        font-size: 14px;
        margin-left: 8px;
    }
    .added-questions-list li .actions a:hover { text-decoration: underline; }
    .added-questions-list li .actions a.move-btn {
        color: #007bff;
        font-size: 12px;
        padding: 2px 6px;
        border: 1px solid #007bff;
        border-radius: 3px;
        background-color: #fff;
    }
    .added-questions-list li .actions a.move-btn:hover {
        background-color: #007bff;
        color: white;
        text-decoration: none;
    }
    .added-questions-list li .actions a.move-btn:first-child {
        margin-left: 0;
    }

    .message { padding: 10px; margin-bottom: 15px; border-radius: 4px; font-size: 14px; }
    .error-message { background-color: #ffebee; color: #c62828; border: 1px solid #ef9a9a; }
    .success-message { background-color: #e8f5e9; color: #2e7d32; border: 1px solid #a5d6a7; }
</style>

<div class="question-creator-container">
    <div class="question-types-sidebar">
        <h3>选择题型</h3>
        <ul>
            <li><a href="dashboard.php?page=create_questionnaire_questions&type=name" class="<?php echo $current_selected_type == 'name' ? 'active' : ''; ?>">姓名</a></li>
            <li><a href="dashboard.php?page=create_questionnaire_questions&type=id_card" class="<?php echo $current_selected_type == 'id_card' ? 'active' : ''; ?>">身份证号码</a></li>
            <li><a href="dashboard.php?page=create_questionnaire_questions&type=phone" class="<?php echo $current_selected_type == 'phone' ? 'active' : ''; ?>">手机</a></li>
            <li><a href="dashboard.php?page=create_questionnaire_questions&type=single_choice" class="<?php echo $current_selected_type == 'single_choice' ? 'active' : ''; ?>">单选题</a></li>
            <li><a href="dashboard.php?page=create_questionnaire_questions&type=fill_blank" class="<?php echo $current_selected_type == 'fill_blank' ? 'active' : ''; ?>">单项填空</a></li>
        </ul>
    </div>

    <div class="question-settings-main">
        <h1>添加问题到问卷</h1>
        <div class="questionnaire-title-display">当前问卷: <?php echo htmlspecialchars($questionnaire_title); ?> (ID: <?php echo htmlspecialchars($questionnaire_id); ?>)</div>

        <?php if (!empty($error_message)): ?>
            <div class="message error-message"><?php echo $error_message; ?></div>
        <?php endif; ?>
        <?php if (!empty($success_message)): ?>
            <div class="message success-message"><?php echo $success_message; ?></div>
        <?php endif; ?>

        <?php if (!empty($current_selected_type)): ?>
        <form action="dashboard.php?page=create_questionnaire_questions&type=<?php echo $current_selected_type; ?>" method="post">
            <input type="hidden" name="question_type" value="<?php echo $current_selected_type; ?>">

            <?php
            $default_label = '';
            $label_placeholder = '请输入题目内容';
            $can_edit_label = true;
            switch ($current_selected_type) {
                case 'name':
                    $default_label = '姓名';
                    $label_placeholder = '例如：您的姓名 (可修改)';
                    break;
                case 'id_card':
                    $default_label = '身份证号码';
                    $label_placeholder = '例如：您的身份证号 (可修改)';
                    break;
                case 'phone':
                    $default_label = '手机';
                    $label_placeholder = '例如：您的手机号码 (可修改)';
                    break;
                case 'single_choice':
                    $label_placeholder = '请输入单选题的题目';
                    break;
                case 'fill_blank':
                    $label_placeholder = '请输入填空题的题目';
                    break;
            }
            ?>

            <div class="form-section">
                <label for="question_label">
                    <?php
                        echo ($current_selected_type == 'name' || $current_selected_type == 'id_card' || $current_selected_type == 'phone') ? '字段提示文字 (可修改):' : '题目:';
                    ?>
                </label>
                <input type="text" id="question_label" name="question_label" placeholder="<?php echo $label_placeholder; ?>" value="<?php echo $default_label; ?>" <?php echo ($current_selected_type != 'name' && $current_selected_type != 'id_card' && $current_selected_type != 'phone' && $current_selected_type != 'single_choice' && $current_selected_type != 'fill_blank') ? '' : 'required'; ?>>
                 <small style="display:block; margin-top:5px; color:#777;">
                    <?php
                        if ($current_selected_type == 'name') echo "前台验证：中文姓名";
                        elseif ($current_selected_type == 'id_card') echo "前台验证：中国身份证号码";
                        elseif ($current_selected_type == 'phone') echo "前台验证：中国手机号码";
                    ?>
                </small>
            </div>


            <?php if ($current_selected_type == 'single_choice'): ?>
            <div class="form-section options-container">
                <label>选项内容:</label>
                <div id="options-wrapper">
                    <div class="option-item">
                        <input type="text" name="options[]" placeholder="选项1" required>
                        <button type="button" class="btn-remove-option" onclick="removeOption(this)" style="display:none;">删除</button>
                    </div>
                    <div class="option-item">
                        <input type="text" name="options[]" placeholder="选项2" required>
                        <button type="button" class="btn-remove-option" onclick="removeOption(this)">删除</button>
                    </div>
                </div>
                <button type="button" class="btn-add-option" onclick="addOption()">添加选项</button>
            </div>
            <?php endif; ?>

            <?php if ($current_selected_type == 'fill_blank'): ?>
                <!-- 单项填空题，题目在上面已处理，这里可以加一些提示或特定设置 -->
                <p style="color:#555; font-size:14px;">此类型将在前台显示为一个文本输入框。</p>
            <?php endif; ?>

            <button type="submit" name="add_question" class="btn-save-question">添加到问卷</button>
        </form>
        <?php else: ?>
            <p>请从左侧选择一个题型开始添加问题。</p>
        <?php endif; ?>


        <div class="added-questions-list">
            <h3>已添加的问题 (<?php echo count($questions); ?>)</h3>
            <?php if (!empty($questions)): ?>
            <ul>
                <?php foreach ($questions as $index => $q): ?>
                <li>
                    <div class="question-info">
                        <span class="question-label"><?php echo ($index + 1) . '. ' . htmlspecialchars($q['label']); ?></span>
                        <span class="question-type-badge">
                            <?php
                            switch ($q['type']) {
                                case 'name': echo '姓名'; break;
                                case 'id_card': echo '身份证'; break;
                                case 'phone': echo '手机'; break;
                                case 'single_choice': echo '单选题'; break;
                                case 'fill_blank': echo '填空题'; break;
                                default: echo htmlspecialchars($q['type']); break;
                            }
                            ?>
                        </span>
                        <?php if ($q['type'] == 'single_choice' && !empty($q['options'])): ?>
                            <ul style="font-size:0.9em; color:#555; margin-left:20px;">
                            <?php foreach($q['options'] as $opt): ?>
                                <li><?php echo htmlspecialchars($opt); ?></li>
                            <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </div>
                    <div class="actions">
                        <?php if ($index > 0): ?>
                            <a href="dashboard.php?page=create_questionnaire_questions&action=move_up&question_id=<?php echo $q['id']; ?>" class="move-btn" title="上移">↑</a>
                        <?php endif; ?>
                        <?php if ($index < count($questions) - 1): ?>
                            <a href="dashboard.php?page=create_questionnaire_questions&action=move_down&question_id=<?php echo $q['id']; ?>" class="move-btn" title="下移">↓</a>
                        <?php endif; ?>
                        <!-- 编辑功能后续添加 -->
                        <!-- <a href="dashboard.php?page=create_questionnaire_questions&action=edit_question&question_id=<?php echo $q['id']; ?>">编辑</a> | -->
                        <a href="dashboard.php?page=create_questionnaire_questions&action=delete_question&question_id=<?php echo $q['id']; ?>" onclick="return confirm('确定要删除这个问题吗？');">删除</a>
                    </div>
                </li>
                <?php endforeach; ?>
            </ul>
            <form action="dashboard.php?page=create_questionnaire_questions" method="post" style="margin-top:20px;">
                <button type="submit" name="save_questionnaire" class="btn-save-questionnaire">保存问卷</button>
            </form>
            <div style="clear:both;"></div>
            <?php else: ?>
            <p>暂无问题。请从上方选择题型并添加。</p>
            <?php endif; ?>
        </div>

    </div> <!-- end .question-settings-main -->
</div> <!-- end .question-creator-container -->

<script>
function addOption() {
    const wrapper = document.getElementById('options-wrapper');
    const optionItems = wrapper.getElementsByClassName('option-item');
    const newOption = document.createElement('div');
    newOption.className = 'option-item';
    newOption.innerHTML = `
        <input type="text" name="options[]" placeholder="选项 ${optionItems.length + 1}" required>
        <button type="button" class="btn-remove-option" onclick="removeOption(this)">删除</button>
    `;
    wrapper.appendChild(newOption);
    updateRemoveButtons();
}

function removeOption(button) {
    const optionItem = button.parentNode;
    optionItem.parentNode.removeChild(optionItem);
    updateRemoveButtons();
    // 更新 placeholder
    const wrapper = document.getElementById('options-wrapper');
    const optionInputs = wrapper.querySelectorAll('.option-item input[type="text"]');
    optionInputs.forEach((input, index) => {
        input.placeholder = `选项 ${index + 1}`;
    });
}

function updateRemoveButtons() {
    const wrapper = document.getElementById('options-wrapper');
    const removeButtons = wrapper.getElementsByClassName('btn-remove-option');
    if (removeButtons.length <= 2) {
        for (let btn of removeButtons) {
            btn.style.display = 'none';
        }
    } else {
        for (let btn of removeButtons) {
            btn.style.display = 'inline-block';
        }
    }
}

// 初始化时更新一次删除按钮状态
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('options-wrapper')) {
        updateRemoveButtons();
    }
});
</script>
