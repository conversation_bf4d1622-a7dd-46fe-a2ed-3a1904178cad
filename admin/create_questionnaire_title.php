<?php
// 确保此文件被 dashboard.php 正确包含
if (basename(__FILE__) == basename($_SERVER['SCRIPT_FILENAME'])) {
    echo "此页面不能直接访问。";
    exit;
}
// global $mysqli; // 如果需要数据库操作

$error_message = '';
$success_message = '';

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['questionnaire_title'])) {
    $title = trim($_POST['questionnaire_title']);
    $submission_limit = isset($_POST['submission_limit']) && $_POST['submission_limit'] !== '' ? (int)$_POST['submission_limit'] : null;

    // 处理时间设置
    $start_time = null;
    $end_time = null;

    if (isset($_POST['enable_start_time']) && $_POST['enable_start_time'] == '1' && !empty($_POST['start_time'])) {
        $start_time = $_POST['start_time'];
    }

    if (isset($_POST['enable_end_time']) && $_POST['enable_end_time'] == '1' && !empty($_POST['end_time'])) {
        $end_time = $_POST['end_time'];
    }

    if (empty($title)) {
        $error_message = "问卷标题不能为空。";
    } elseif ($submission_limit !== null && $submission_limit <= 0) {
        $error_message = "回收份数限制必须是正整数。";
    } elseif ($start_time && $end_time && strtotime($start_time) >= strtotime($end_time)) {
        $error_message = "开始时间必须早于结束时间。";
    } else {
        // 实际项目中，这里会将标题保存到数据库，并获取新问卷的ID
        // 为了演示，我们先将标题存入 session，并模拟一个ID
        // 生成一个唯一的10位数ID (这里用时间戳+随机数简化)
        $questionnaire_id = substr(time() . rand(100,999), 0, 10); // 简单示例

        $_SESSION['current_questionnaire_id'] = $questionnaire_id;
        $_SESSION['current_questionnaire_title'] = $title;
        $_SESSION['current_questionnaire_submission_limit'] = $submission_limit;
        $_SESSION['current_questionnaire_start_time'] = $start_time;
        $_SESSION['current_questionnaire_end_time'] = $end_time;

        // 清除可能存在的旧问卷问题数据
        unset($_SESSION['current_questionnaire_questions']);

        // 跳转到问题添加页面
        // 注意：dashboard.php 会根据 session 中的 current_questionnaire_id 来加载 create_questionnaire_questions.php
        header("Location: dashboard.php?page=create_questionnaire_questions");
        exit;
    }
}
?>
<style>
    .form-container {
        background-color: #fff;
        padding: 25px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        max-width: 600px;
        margin: 20px auto;
    }
    .form-container h1 {
        color: #0050b3;
        margin-bottom: 20px;
        font-size: 22px;
    }
    .form-container label {
        display: block;
        margin-bottom: 8px;
        font-weight: bold;
        color: #333;
    }
    .form-container input[type="text"],
    .form-container input[type="number"],
    .form-container input[type="datetime-local"] {
        width: 100%;
        padding: 10px;
        margin-bottom: 5px;
        border: 1px solid #b3d9ff;
        border-radius: 4px;
        box-sizing: border-box;
    }
    .form-container input[type="text"]:focus,
    .form-container input[type="number"]:focus,
    .form-container input[type="datetime-local"]:focus {
        border-color: #007bff;
        outline: none;
    }
    .time-setting {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }
    .time-setting h3 {
        margin: 0 0 15px 0;
        color: #0050b3;
        font-size: 16px;
    }
    .checkbox-group {
        margin-bottom: 10px;
    }
    .checkbox-group input[type="checkbox"] {
        margin-right: 8px;
    }
    .time-input-group {
        margin-left: 25px;
        margin-top: 10px;
    }
    .time-input-group input[type="datetime-local"] {
        width: 250px;
    }
    .form-container button {
        background-color: #007bff;
        color: white;
        padding: 10px 18px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        transition: background-color 0.3s ease;
    }
    .form-container button:hover {
        background-color: #0056b3;
    }
    .message {
        padding: 10px;
        margin-bottom: 15px;
        border-radius: 4px;
        font-size: 14px;
    }
    .error-message {
        background-color: #ffebee;
        color: #c62828;
        border: 1px solid #ef9a9a;
    }
    .success-message {
        background-color: #e8f5e9;
        color: #2e7d32;
        border: 1px solid #a5d6a7;
    }
</style>

<div class="form-container">
    <h1>创建新问卷 - 输入标题</h1>

    <?php if (!empty($error_message)): ?>
        <div class="message error-message"><?php echo $error_message; ?></div>
    <?php endif; ?>
    <?php if (!empty($success_message)): ?>
        <div class="message success-message"><?php echo $success_message; ?></div>
    <?php endif; ?>

    <form action="dashboard.php?page=create_questionnaire_title" method="post">
        <label for="questionnaire_title">问卷标题:</label>
        <input type="text" id="questionnaire_title" name="questionnaire_title" value="<?php echo isset($_SESSION['current_questionnaire_title']) ? htmlspecialchars($_SESSION['current_questionnaire_title']) : ''; ?>" required>

        <label for="submission_limit">回收份数限制:</label>
        <input type="number" id="submission_limit" name="submission_limit" min="1" placeholder="留空表示无限制" value="<?php echo isset($_SESSION['current_questionnaire_submission_limit']) ? $_SESSION['current_questionnaire_submission_limit'] : ''; ?>">
        <small style="color: #666; font-size: 12px; display: block; margin-bottom: 20px;">设置问卷最多可以回收多少份，留空表示无限制</small>

        <div class="time-setting">
            <h3>时间设置</h3>

            <div class="checkbox-group">
                <label>
                    <input type="checkbox" id="enable_start_time" name="enable_start_time" value="1"
                           <?php echo (isset($_SESSION['current_questionnaire_start_time']) && $_SESSION['current_questionnaire_start_time']) ? 'checked' : ''; ?>
                           onchange="toggleTimeInput('start')">
                    设置开始时间
                </label>
                <div class="time-input-group" id="start_time_group" style="display: <?php echo (isset($_SESSION['current_questionnaire_start_time']) && $_SESSION['current_questionnaire_start_time']) ? 'block' : 'none'; ?>;">
                    <input type="datetime-local" id="start_time" name="start_time"
                           value="<?php echo isset($_SESSION['current_questionnaire_start_time']) ? $_SESSION['current_questionnaire_start_time'] : ''; ?>">
                    <small style="color: #666; font-size: 12px; display: block;">问卷在此时间之前不可填写</small>
                </div>
            </div>

            <div class="checkbox-group">
                <label>
                    <input type="checkbox" id="enable_end_time" name="enable_end_time" value="1"
                           <?php echo (isset($_SESSION['current_questionnaire_end_time']) && $_SESSION['current_questionnaire_end_time']) ? 'checked' : ''; ?>
                           onchange="toggleTimeInput('end')">
                    设置结束时间
                </label>
                <div class="time-input-group" id="end_time_group" style="display: <?php echo (isset($_SESSION['current_questionnaire_end_time']) && $_SESSION['current_questionnaire_end_time']) ? 'block' : 'none'; ?>;">
                    <input type="datetime-local" id="end_time" name="end_time"
                           value="<?php echo isset($_SESSION['current_questionnaire_end_time']) ? $_SESSION['current_questionnaire_end_time'] : ''; ?>">
                    <small style="color: #666; font-size: 12px; display: block;">问卷在此时间之后不可填写</small>
                </div>
            </div>
        </div>

        <button type="submit">下一步：添加问题</button>
    </form>
</div>

<script>
function toggleTimeInput(type) {
    const checkbox = document.getElementById('enable_' + type + '_time');
    const group = document.getElementById(type + '_time_group');
    const input = document.getElementById(type + '_time');

    if (checkbox.checked) {
        group.style.display = 'block';
        // 设置默认时间为服务器当前时间
        if (!input.value) {
            // 使用服务器时间作为默认值
            const serverTime = new Date(<?php echo time() * 1000; ?>);
            const year = serverTime.getFullYear();
            const month = String(serverTime.getMonth() + 1).padStart(2, '0');
            const day = String(serverTime.getDate()).padStart(2, '0');
            const hours = String(serverTime.getHours()).padStart(2, '0');
            const minutes = String(serverTime.getMinutes()).padStart(2, '0');
            input.value = `${year}-${month}-${day}T${hours}:${minutes}`;
        }
    } else {
        group.style.display = 'none';
        input.value = '';
    }
}
</script>
