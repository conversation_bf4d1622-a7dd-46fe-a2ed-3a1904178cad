<?php
session_start();
include_once '../config/db_config.php';

// 检查用户是否已登录，否则重定向到登录页面
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header("Location: login.php");
    exit;
}

// 处理退出登录
if (isset($_GET['action']) && $_GET['action'] == 'logout') {
    session_unset();
    session_destroy();
    header("Location: login.php");
    exit;
}

// 根据GET参数确定右侧显示的内容
$page_content = 'questionnaire_list.php'; // 默认显示问卷列表
if (isset($_GET['page'])) {
    if ($_GET['page'] == 'create_questionnaire_title') {
        $page_content = 'create_questionnaire_title.php';
    } elseif ($_GET['page'] == 'create_questionnaire_questions') {
        // 这个页面需要问卷ID，将在创建标题后处理
        // 暂时不做处理，或者需要传递问卷ID
        if (isset($_SESSION['current_questionnaire_id'])) {
             $page_content = 'create_questionnaire_questions.php';
        } else {
            // 如果没有问卷ID，则重定向回创建标题页面或列表页
            $page_content = 'create_questionnaire_title.php';
        }
    } elseif ($_GET['page'] == 'all_questionnaires') {
        $page_content = 'questionnaire_list.php'; // 全部问卷也显示问卷列表
    } elseif ($_GET['page'] == 'recycle_bin') {
        $page_content = 'recycle_bin.php';
    } elseif ($_GET['page'] == 'edit_questionnaire') {
        // 确保问卷ID已传递，否则重定向或显示错误
        if (isset($_GET['id'])) {
            $page_content = 'edit_questionnaire.php';
        } else {
            // 如果没有ID，则重定向到问卷列表，并可能显示一个错误消息
            $_SESSION['message'] = ['type' => 'error', 'text' => '编辑问卷需要提供问卷ID。'];
            header("Location: dashboard.php?page=all_questionnaires");
            exit;
        }
    } elseif ($_GET['page'] == 'preview_questionnaire') {
        // 确保问卷ID已传递
        if (isset($_GET['id'])) {
            $page_content = 'preview_questionnaire.php';
        } else {
            $_SESSION['message'] = ['type' => 'error', 'text' => '预览问卷需要提供问卷ID。'];
            header("Location: dashboard.php?page=all_questionnaires");
            exit;
        }
    } elseif ($_GET['page'] == 'view_submissions') {
        // 确保问卷ID已传递
        if (isset($_GET['id'])) {
            $page_content = 'view_submissions.php';
        } else {
            $_SESSION['message'] = ['type' => 'error', 'text' => '查看答卷需要提供问卷ID。'];
            header("Location: dashboard.php?page=all_questionnaires");
            exit;
        }
    }
    // 其他页面可以在这里添加
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理 - 问卷系统</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f0f2f5; /* 浅灰色背景 */
            margin: 0;
            display: flex;
            height: 100vh;
            color: #333;
        }
        .sidebar {
            width: 220px;
            background-color: #001529; /* 深蓝导航栏背景 */
            color: white;
            padding-top: 20px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .sidebar h2 {
            text-align: center;
            color: #fff;
            margin-bottom: 30px;
            font-size: 20px;
        }
        .sidebar a {
            display: block;
            color: rgba(255, 255, 255, 0.8);
            padding: 15px 20px;
            text-decoration: none;
            font-size: 16px;
            border-left: 3px solid transparent;
            transition: background-color 0.3s ease, color 0.3s ease, border-left-color 0.3s ease;
        }
        .sidebar a:hover, .sidebar a.active {
            background-color: #007bff; /* 悬停或激活时主蓝色背景 */
            color: #fff;
            border-left-color: #096dd9; /* 激活时边框颜色 */
        }

        .main-content {
            flex-grow: 1;
            padding: 30px;
            background-color: #ffffff;
            overflow-y: auto; /* 内容过多时可滚动 */
            border-left: 1px solid #e8e8e8;
        }
        .main-content h1 {
            color: #0050b3; /* 深蓝色标题 */
            margin-bottom: 20px;
            border-bottom: 2px solid #e6f7ff;
            padding-bottom: 10px;
        }
        /* 可以在这里添加更多通用样式 */
    </style>
</head>
<body>
    <div class="sidebar">
        <h2>问卷系统后台</h2>
        <a href="dashboard.php?page=create_questionnaire_title" class="<?php echo (strpos($page_content, 'create_questionnaire_title') !== false || strpos($page_content, 'create_questionnaire_questions') !== false) ? 'active' : ''; ?>">创建问卷</a>
        <a href="dashboard.php?page=all_questionnaires" class="<?php echo ($page_content == 'questionnaire_list.php') ? 'active' : ''; ?>">全部问卷</a>
        <a href="dashboard.php?page=recycle_bin" class="<?php echo ($page_content == 'recycle_bin.php') ? 'active' : ''; ?>">回收站</a>
        <a href="change_password.php">修改密码</a>
        <a href="dashboard.php?action=logout">退出登录</a>
    </div>
    <div class="main-content">
        <?php
        // 动态加载右侧内容区域的 PHP 文件
        if (file_exists($page_content)) {
            include $page_content;
        } else {
            echo "<h1>欢迎来到后台管理系统</h1>";
            echo "<p>请从左侧菜单选择一个操作。</p>";
            // 如果默认的 questionnaire_list.php 不存在，显示此消息
            if ($page_content == 'questionnaire_list.php' && !file_exists('questionnaire_list.php')) {
                 echo "<p style='color:red;'>错误：问卷列表文件 (questionnaire_list.php) 未找到。</p>";
            }
        }
        ?>
    </div>
</body>
</html>
