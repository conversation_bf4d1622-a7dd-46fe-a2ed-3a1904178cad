<?php
// 测试修改密码功能
session_start();
include_once 'config/db_config.php';

echo "测试修改密码功能\n";
echo "==================\n";

// 模拟登录状态
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_username'] = 'admin';
$_SESSION['admin_id'] = 1;

echo "模拟登录状态设置完成\n";

// 测试修改密码逻辑
$admin_id = $_SESSION['admin_id'];
$current_password = 'admin';
$new_password = 'newpassword123';

echo "当前密码: " . $current_password . "\n";
echo "新密码: " . $new_password . "\n";

// 验证当前密码
$stmt = $mysqli->prepare("SELECT password FROM admin_users WHERE id = ?");
if ($stmt) {
    $stmt->bind_param("i", $admin_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 1) {
        $admin = $result->fetch_assoc();
        echo "数据库中的密码哈希: " . $admin['password'] . "\n";
        echo "当前密码的MD5: " . md5($current_password) . "\n";
        
        if ($admin['password'] === md5($current_password)) {
            echo "当前密码验证成功！\n";
            
            // 更新密码
            $new_password_hash = md5($new_password);
            echo "新密码的MD5: " . $new_password_hash . "\n";
            
            $update_stmt = $mysqli->prepare("UPDATE admin_users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            if ($update_stmt) {
                $update_stmt->bind_param("si", $new_password_hash, $admin_id);
                if ($update_stmt->execute()) {
                    echo "密码修改成功！\n";
                    
                    // 验证新密码
                    $verify_stmt = $mysqli->prepare("SELECT password FROM admin_users WHERE id = ?");
                    if ($verify_stmt) {
                        $verify_stmt->bind_param("i", $admin_id);
                        $verify_stmt->execute();
                        $verify_result = $verify_stmt->get_result();
                        $verify_admin = $verify_result->fetch_assoc();
                        
                        echo "更新后的密码哈希: " . $verify_admin['password'] . "\n";
                        
                        if ($verify_admin['password'] === md5($new_password)) {
                            echo "新密码验证成功！\n";
                        } else {
                            echo "新密码验证失败！\n";
                        }
                        $verify_stmt->close();
                    }
                } else {
                    echo "密码修改失败: " . $update_stmt->error . "\n";
                }
                $update_stmt->close();
            } else {
                echo "准备更新语句失败: " . $mysqli->error . "\n";
            }
        } else {
            echo "当前密码验证失败！\n";
        }
    } else {
        echo "用户不存在\n";
    }
    $stmt->close();
} else {
    echo "数据库查询失败: " . $mysqli->error . "\n";
}

// 恢复原密码以便后续测试
echo "\n恢复原密码...\n";
$original_password_hash = md5('admin');
$restore_stmt = $mysqli->prepare("UPDATE admin_users SET password = ? WHERE id = ?");
if ($restore_stmt) {
    $restore_stmt->bind_param("si", $original_password_hash, $admin_id);
    if ($restore_stmt->execute()) {
        echo "原密码恢复成功！\n";
    } else {
        echo "原密码恢复失败: " . $restore_stmt->error . "\n";
    }
    $restore_stmt->close();
}

$mysqli->close();
?>
