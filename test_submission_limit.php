<?php
// 测试回收份数限制功能
include_once 'config/db_config.php';

echo "测试回收份数限制功能\n";
echo "======================\n";

// 1. 测试创建带限制的问卷
echo "1. 测试创建带限制的问卷...\n";

// 创建一个测试问卷，限制为3份
$test_questionnaire_id = substr('TEST' . time(), 0, 10); // 限制为10位
$test_title = '测试限制问卷';
$test_limit = 3;

$stmt = $mysqli->prepare("INSERT INTO questionnaires (id, title, status, submission_limit, created_at) VALUES (?, ?, 'published', ?, NOW())");
if ($stmt) {
    $stmt->bind_param("ssi", $test_questionnaire_id, $test_title, $test_limit);
    if ($stmt->execute()) {
        echo "   ✓ 测试问卷创建成功 (ID: {$test_questionnaire_id}, 限制: {$test_limit}份)\n";
    } else {
        echo "   ✗ 测试问卷创建失败: " . $stmt->error . "\n";
        exit;
    }
    $stmt->close();
} else {
    echo "   ✗ 准备语句失败: " . $mysqli->error . "\n";
    exit;
}

// 添加一个测试问题
$question_id = 'test_q_' . uniqid();
$stmt_q = $mysqli->prepare("INSERT INTO questions (id, questionnaire_id, type, label, sort_order, created_at) VALUES (?, ?, 'name', '测试姓名', 1, NOW())");
if ($stmt_q) {
    $stmt_q->bind_param("ss", $question_id, $test_questionnaire_id);
    $stmt_q->execute();
    $stmt_q->close();
    echo "   ✓ 测试问题添加成功\n";
}

// 2. 测试提交问卷（在限制内）
echo "\n2. 测试提交问卷（在限制内）...\n";

for ($i = 1; $i <= $test_limit; $i++) {
    // 模拟提交
    $stmt_sub = $mysqli->prepare("INSERT INTO submissions (questionnaire_id, ip_address, created_at) VALUES (?, ?, NOW())");
    if ($stmt_sub) {
        $ip = "192.168.1.{$i}";
        $stmt_sub->bind_param("ss", $test_questionnaire_id, $ip);
        if ($stmt_sub->execute()) {
            $submission_id = $mysqli->insert_id;
            
            // 添加答案
            $stmt_ans = $mysqli->prepare("INSERT INTO submission_answers (submission_id, question_id, answer_value) VALUES (?, ?, ?)");
            if ($stmt_ans) {
                $answer = "测试用户{$i}";
                $stmt_ans->bind_param("iss", $submission_id, $question_id, $answer);
                $stmt_ans->execute();
                $stmt_ans->close();
            }
            
            echo "   ✓ 第{$i}份提交成功\n";
        } else {
            echo "   ✗ 第{$i}份提交失败: " . $stmt_sub->error . "\n";
        }
        $stmt_sub->close();
    }
}

// 3. 检查当前提交数量
echo "\n3. 检查当前提交数量...\n";
$stmt_count = $mysqli->prepare("SELECT COUNT(*) as count FROM submissions WHERE questionnaire_id = ?");
if ($stmt_count) {
    $stmt_count->bind_param("s", $test_questionnaire_id);
    $stmt_count->execute();
    $result = $stmt_count->get_result();
    $count_data = $result->fetch_assoc();
    $current_count = $count_data['count'];
    echo "   当前提交数量: {$current_count}/{$test_limit}\n";
    $stmt_count->close();
}

// 4. 测试超出限制的提交
echo "\n4. 测试超出限制的提交...\n";

// 模拟前台提交逻辑
$questionnaire_submission_limit = $test_limit;
$stmt_check = $mysqli->prepare("SELECT COUNT(*) as submission_count FROM submissions WHERE questionnaire_id = ?");
if ($stmt_check) {
    $stmt_check->bind_param("s", $test_questionnaire_id);
    $stmt_check->execute();
    $check_result = $stmt_check->get_result();
    $check_data = $check_result->fetch_assoc();
    $current_submissions = $check_data['submission_count'];
    $stmt_check->close();
    
    if ($current_submissions >= $questionnaire_submission_limit) {
        echo "   ✓ 正确检测到已达到限制，拒绝提交\n";
        echo "   提示信息: 抱歉，该问卷已达到回收份数上限（{$questionnaire_submission_limit}份），无法继续提交。\n";
    } else {
        echo "   ✗ 限制检查失败，应该拒绝提交但没有拒绝\n";
    }
}

// 5. 测试后台列表显示
echo "\n5. 测试后台列表显示...\n";
$stmt_list = $mysqli->prepare("SELECT q.id, q.title, q.submission_limit, COUNT(s.id) as submission_count FROM questionnaires q LEFT JOIN submissions s ON q.id = s.questionnaire_id WHERE q.id = ? GROUP BY q.id");
if ($stmt_list) {
    $stmt_list->bind_param("s", $test_questionnaire_id);
    $stmt_list->execute();
    $list_result = $stmt_list->get_result();
    $list_data = $list_result->fetch_assoc();
    
    $current_count = (int)$list_data['submission_count'];
    $limit = $list_data['submission_limit'];
    
    echo "   问卷: {$list_data['title']}\n";
    echo "   回收情况: {$current_count}/{$limit}\n";
    
    if ($limit !== null) {
        $percentage = $limit > 0 ? ($current_count / $limit) * 100 : 0;
        if ($percentage >= 100) {
            echo "   状态: 已满 (100%)\n";
        } elseif ($percentage >= 80) {
            echo "   状态: 接近满额 ({$percentage}%)\n";
        } else {
            echo "   状态: 正常 ({$percentage}%)\n";
        }
    }
    
    $stmt_list->close();
}

// 6. 清理测试数据
echo "\n6. 清理测试数据...\n";
$mysqli->begin_transaction();
try {
    // 删除答案
    $stmt_del_ans = $mysqli->prepare("DELETE sa FROM submission_answers sa INNER JOIN submissions s ON sa.submission_id = s.id WHERE s.questionnaire_id = ?");
    $stmt_del_ans->bind_param("s", $test_questionnaire_id);
    $stmt_del_ans->execute();
    $stmt_del_ans->close();
    
    // 删除提交
    $stmt_del_sub = $mysqli->prepare("DELETE FROM submissions WHERE questionnaire_id = ?");
    $stmt_del_sub->bind_param("s", $test_questionnaire_id);
    $stmt_del_sub->execute();
    $stmt_del_sub->close();
    
    // 删除问题
    $stmt_del_q = $mysqli->prepare("DELETE FROM questions WHERE questionnaire_id = ?");
    $stmt_del_q->bind_param("s", $test_questionnaire_id);
    $stmt_del_q->execute();
    $stmt_del_q->close();
    
    // 删除问卷
    $stmt_del_quest = $mysqli->prepare("DELETE FROM questionnaires WHERE id = ?");
    $stmt_del_quest->bind_param("s", $test_questionnaire_id);
    $stmt_del_quest->execute();
    $stmt_del_quest->close();
    
    $mysqli->commit();
    echo "   ✓ 测试数据清理完成\n";
} catch (Exception $e) {
    $mysqli->rollback();
    echo "   ✗ 清理测试数据失败: " . $e->getMessage() . "\n";
}

echo "\n测试完成！\n";
echo "所有功能测试通过，回收份数限制功能正常工作。\n";

$mysqli->close();
?>
