<?php
// 完整的修改密码测试
session_start();
include_once 'config/db_config.php';

echo "完整的修改密码功能测试\n";
echo "========================\n";

// 模拟登录状态
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_username'] = 'admin';
$_SESSION['admin_id'] = 1;

echo "1. 模拟登录状态设置完成\n";

// 模拟POST请求数据
$_SERVER["REQUEST_METHOD"] = "POST";
$_POST['current_password'] = 'admin';
$_POST['new_password'] = 'newpassword123';
$_POST['confirm_password'] = 'newpassword123';

echo "2. 模拟表单提交数据设置完成\n";
echo "   当前密码: " . $_POST['current_password'] . "\n";
echo "   新密码: " . $_POST['new_password'] . "\n";
echo "   确认密码: " . $_POST['confirm_password'] . "\n";

// 开始输出缓冲，捕获页面输出
ob_start();

// 包含修改密码页面（这会执行表单处理逻辑）
include 'admin/change_password.php';

// 获取页面输出
$output = ob_get_clean();

echo "3. 修改密码页面处理完成\n";

// 检查输出中是否包含成功消息
if (strpos($output, '密码修改成功') !== false) {
    echo "4. ✓ 页面显示密码修改成功消息\n";
} else {
    echo "4. ✗ 页面未显示成功消息\n";
    echo "页面输出片段:\n";
    echo substr($output, 0, 500) . "...\n";
}

// 验证数据库中的密码是否已更改
$stmt = $mysqli->prepare("SELECT password FROM admin_users WHERE id = ?");
if ($stmt) {
    $stmt->bind_param("i", $_SESSION['admin_id']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 1) {
        $admin = $result->fetch_assoc();
        $expected_new_hash = md5('newpassword123');
        
        if ($admin['password'] === $expected_new_hash) {
            echo "5. ✓ 数据库中的密码已成功更新\n";
        } else {
            echo "5. ✗ 数据库中的密码未更新\n";
            echo "   期望的哈希: " . $expected_new_hash . "\n";
            echo "   实际的哈希: " . $admin['password'] . "\n";
        }
    } else {
        echo "5. ✗ 无法从数据库获取用户信息\n";
    }
    $stmt->close();
} else {
    echo "5. ✗ 数据库查询失败\n";
}

// 测试用新密码登录
echo "6. 测试新密码登录...\n";
$login_stmt = $mysqli->prepare("SELECT id, username, password FROM admin_users WHERE username = ?");
if ($login_stmt) {
    $username = 'admin';
    $login_stmt->bind_param("s", $username);
    $login_stmt->execute();
    $login_result = $login_stmt->get_result();
    
    if ($login_result->num_rows == 1) {
        $login_admin = $login_result->fetch_assoc();
        if ($login_admin['password'] === md5('newpassword123')) {
            echo "   ✓ 新密码登录验证成功\n";
        } else {
            echo "   ✗ 新密码登录验证失败\n";
        }
    }
    $login_stmt->close();
}

// 恢复原密码
echo "7. 恢复原密码...\n";
$original_password_hash = md5('admin');
$restore_stmt = $mysqli->prepare("UPDATE admin_users SET password = ? WHERE id = ?");
if ($restore_stmt) {
    $restore_stmt->bind_param("si", $original_password_hash, $_SESSION['admin_id']);
    if ($restore_stmt->execute()) {
        echo "   ✓ 原密码恢复成功\n";
    } else {
        echo "   ✗ 原密码恢复失败: " . $restore_stmt->error . "\n";
    }
    $restore_stmt->close();
}

echo "\n测试完成！\n";
$mysqli->close();
?>
