<?php
// 修复管理员密码
include_once 'config/db_config.php';

echo "修复管理员密码\n";
echo "================\n";

// 更新admin用户的密码
$correct_password_hash = md5('admin');
echo "正确的密码哈希: " . $correct_password_hash . "\n";

$stmt = $mysqli->prepare("UPDATE admin_users SET password = ? WHERE username IN ('admin', 'Michael')");
if ($stmt) {
    $stmt->bind_param("s", $correct_password_hash);
    if ($stmt->execute()) {
        echo "密码更新成功，影响行数: " . $stmt->affected_rows . "\n";
    } else {
        echo "密码更新失败: " . $stmt->error . "\n";
    }
    $stmt->close();
} else {
    echo "准备语句失败: " . $mysqli->error . "\n";
}

// 验证更新结果
$result = $mysqli->query("SELECT username, password FROM admin_users");
echo "\n更新后的管理员账户:\n";
while ($row = $result->fetch_assoc()) {
    echo "用户名: " . $row['username'] . ", 密码哈希: " . $row['password'] . "\n";
}

$mysqli->close();
?>
