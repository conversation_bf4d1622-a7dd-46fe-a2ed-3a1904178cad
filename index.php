<?php
session_start();
include_once 'config/db_config.php'; // 包含数据库配置文件

$questionnaire_id = $_GET['id'] ?? null;
$questionnaire_title = '';
$questionnaire_submission_limit = null;
$questions = [];
$error_message = '';

if (empty($questionnaire_id)) {
    $error_message = "错误：未提供问卷ID。";
} else {
    // 1. 获取问卷标题和回收份数限制
    $stmt_q = $mysqli->prepare("SELECT title, submission_limit FROM questionnaires WHERE id = ? AND status = 'published'"); // 通常只显示已发布的问卷
    if (!$stmt_q) {
        $error_message = "数据库查询准备失败 (问卷标题): " . $mysqli->error;
    } else {
        $stmt_q->bind_param("s", $questionnaire_id);
        $stmt_q->execute();
        $result_q = $stmt_q->get_result();
        if ($result_q->num_rows > 0) {
            $questionnaire_data = $result_q->fetch_assoc();
            $questionnaire_title = $questionnaire_data['title'];
            $questionnaire_submission_limit = $questionnaire_data['submission_limit'];
        } else {
            $error_message = "未找到指定的问卷，或者该问卷尚未发布。";
        }
        $stmt_q->close();
    }

    // 2. 如果问卷标题成功获取，则获取问题列表
    if (empty($error_message)) {
        $stmt_qs = $mysqli->prepare("SELECT id, type, label, options FROM questions WHERE questionnaire_id = ? ORDER BY sort_order ASC");
        if (!$stmt_qs) {
            $error_message = "数据库查询准备失败 (问卷问题): " . $mysqli->error;
        } else {
            $stmt_qs->bind_param("s", $questionnaire_id);
            $stmt_qs->execute();
            $result_qs = $stmt_qs->get_result();
            while ($row = $result_qs->fetch_assoc()) {
                if (!empty($row['options'])) {
                    $row['options'] = json_decode($row['options'], true);
                }
                $questions[] = $row;
            }
            $stmt_qs->close();

            if (empty($questions) && empty($error_message)) {
                 // 如果问卷存在但没有问题，可以设置一个提示，但通常这种情况不应该发生如果后台逻辑正确
                 // $error_message = "此问卷当前没有题目。";
            }
        }
    }
}

// 处理问卷提交
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['submit_survey']) && !empty($questionnaire_id) && empty($error_message)) {
    $answers = $_POST['answers'] ?? [];
    $form_errors = [];

    // 后端验证 (示例 - 实际验证会更复杂)
    foreach ($questions as $question) {
        $answer_key = 'q_' . $question['id'];
        $user_answer = trim($answers[$answer_key] ?? '');

        if (empty($user_answer)) {
            $form_errors[$answer_key] = htmlspecialchars($question['label']) . " 不能为空。";
            continue;
        }

        switch ($question['type']) {
            case 'name':
                // 简单中文名判断 (至少两个汉字)
                if (!preg_match('/^[\x{4e00}-\x{9fa5}]{2,}$/u', $user_answer)) {
                    $form_errors[$answer_key] = htmlspecialchars($question['label']) . " 必须是有效的中文姓名。";
                }
                break;
            case 'id_card':
                // 简单18位身份证号判断 (数字或X结尾)
                if (!preg_match('/^\d{17}(\d|X|x)$/', $user_answer)) {
                    $form_errors[$answer_key] = htmlspecialchars($question['label']) . " 必须是有效的18位身份证号码。";
                }
                break;
            case 'phone':
                // 简单11位手机号判断 (以1开头)
                if (!preg_match('/^1\d{10}$/', $user_answer)) {
                    $form_errors[$answer_key] = htmlspecialchars($question['label']) . " 必须是有效的11位手机号码。";
                }
                break;
            // 其他类型验证可以按需添加
        }
    }

    if (empty($form_errors)) {
        // 检查回收份数限制
        if ($questionnaire_submission_limit !== null) {
            $stmt_count = $mysqli->prepare("SELECT COUNT(*) as submission_count FROM submissions WHERE questionnaire_id = ?");
            if ($stmt_count) {
                $stmt_count->bind_param("s", $questionnaire_id);
                $stmt_count->execute();
                $count_result = $stmt_count->get_result();
                $count_data = $count_result->fetch_assoc();
                $current_submissions = $count_data['submission_count'];
                $stmt_count->close();

                if ($current_submissions >= $questionnaire_submission_limit) {
                    $error_message = "抱歉，该问卷已达到回收份数上限（{$questionnaire_submission_limit}份），无法继续提交。感谢您的参与！";
                    $form_errors = []; // 清空表单错误，因为这不是表单验证问题
                }
            } else {
                $error_message = "系统错误，无法检查提交限制。请稍后重试。";
            }
        }

        if (empty($error_message)) {
            global $mysqli;
            $ip_address = $_SERVER['REMOTE_ADDR'];

            $mysqli->begin_transaction();
            try {
            // 1. 插入到 submissions 表
            $stmt_sub = $mysqli->prepare("INSERT INTO submissions (questionnaire_id, ip_address) VALUES (?, ?)");
            if (!$stmt_sub) throw new Exception("Prepare submissions insert failed: " . $mysqli->error);
            $stmt_sub->bind_param("ss", $questionnaire_id, $ip_address);
            if (!$stmt_sub->execute()) throw new Exception("Execute submissions insert failed: " . $stmt_sub->error);
            $submission_id = $mysqli->insert_id; // 获取新插入的 submission ID
            $stmt_sub->close();

            // 2. 遍历答案并插入到 submission_answers 表
            foreach ($answers as $question_id_key => $answer_value) {
                if (strpos($question_id_key, 'q_') === 0) { //确保是问题答案
                    $q_id_real = substr($question_id_key, 2); // 移除 'q_' 前缀
                    $trimmed_answer = trim($answer_value);

                    // 查找对应的问题以确认其存在 (可选，但更安全)
                    $question_exists = false;
                    foreach($questions as $q_lookup) {
                        if ($q_lookup['id'] == $q_id_real) {
                            $question_exists = true;
                            break;
                        }
                    }

                    if ($question_exists) {
                        $stmt_ans = $mysqli->prepare("INSERT INTO submission_answers (submission_id, question_id, answer_value) VALUES (?, ?, ?)");
                        if (!$stmt_ans) throw new Exception("Prepare submission_answers insert failed: " . $mysqli->error);
                        $stmt_ans->bind_param("iss", $submission_id, $q_id_real, $trimmed_answer);
                        if (!$stmt_ans->execute()) throw new Exception("Execute submission_answers insert failed for q_id {$q_id_real}: " . $stmt_ans->error);
                        $stmt_ans->close();
                    }
                }
            }

            $mysqli->commit();
            $success_submission_message = "问卷提交成功！感谢您的参与。";
            $questions = []; // 提交成功后不再显示表单
            $_POST = []; // 清空POST数据防止刷新重复提交

        } catch (Exception $e) {
            $mysqli->rollback();
            $error_message = "提交问卷时发生错误，请稍后重试。详情：" . $e->getMessage();
            // 保留 $form_errors 和 $answers 以便用户可以修正
        }
        }
    } else {
        $error_message = "提交失败，请检查以下错误：";
        // $form_errors 会在表单中显示
    }
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo !empty($questionnaire_title) ? htmlspecialchars($questionnaire_title) : '问卷调查'; ?> - 问卷系统</title>
    <style>
        body {
            font-family: 'Arial', 'Microsoft YaHei', sans-serif;
            background-color: #e6f7ff; /* 淡蓝色背景 */
            color: #333;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        .container {
            width: 90%;
            max-width: 800px;
            margin: 30px auto;
            background-color: #ffffff;
            padding: 20px 30px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            flex-grow: 1;
        }
        header h1 {
            color: #0050b3; /* 深蓝色标题 */
            text-align: center;
            margin-bottom: 25px;
            font-size: 28px;
        }
        .question-item {
            margin-bottom: 25px;
            padding: 15px;
            border: 1px solid #b3d9ff; /* 浅蓝色边框 */
            border-radius: 5px;
            background-color: #f7fbff;
        }
        .question-item label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            color: #003a8c;
        }
        .question-item input[type="text"],
        .question-item select,
        .question-item textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
        }
        .question-item input[type="text"]:focus,
        .question-item select:focus,
        .question-item textarea:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .submit-button {
            display: block;
            width: 100%;
            padding: 12px;
            background-color: #007bff; /* 主蓝色按钮 */
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 18px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin-top: 30px;
        }
        .submit-button:hover {
            background-color: #0056b3; /* 悬停时深蓝色 */
        }
        footer {
            text-align: center;
            padding: 20px;
            background-color: #003a8c; /* 深蓝色页脚 */
            color: #e6f7ff; /* 淡蓝色文字 */
            font-size: 14px;
            margin-top: auto;
        }
        .error-message-box, .success-message-box {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
            text-align: center;
        }
        .error-message-box {
            background-color: #ffebee;
            color: #c62828;
            border: 1px solid #ef9a9a;
        }
        .success-message-box {
            background-color: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #a5d6a7;
        }
        .form-field-error {
            color: red;
            font-size: 0.9em;
            margin-top: 5px;
        }

        /* Responsive adjustments */
        @media (max-width: 600px) {
            .container {
                width: 95%;
                padding: 15px 20px;
                margin-top: 15px;
                margin-bottom: 15px;
            }
            header h1 {
                font-size: 22px;
            }
            .question-item input[type="text"],
            .question-item select,
            .question-item textarea {
                font-size: 15px;
            }
            .submit-button {
                font-size: 16px;
            }
        }
    </style>
    <script>
        // 前端验证函数 (可选，后端验证是主要的)
        function validateForm() {
            let isValid = true;
            // 示例：检查必填项
            <?php foreach ($questions as $question): ?>
                const field_<?php echo $question['id']; ?> = document.getElementById('q_<?php echo $question['id']; ?>');
                const error_<?php echo $question['id']; ?> = document.getElementById('error_q_<?php echo $question['id']; ?>');
                if (field_<?php echo $question['id']; ?> && field_<?php echo $question['id']; ?>.value.trim() === '') {
                    if(error_<?php echo $question['id']; ?>) error_<?php echo $question['id']; ?>.textContent = '此项不能为空。';
                    isValid = false;
                } else if (error_<?php echo $question['id']; ?>) {
                    error_<?php echo $question['id']; ?>.textContent = '';
                }

                // 特定类型的前端验证 (更详细的可以在这里添加)
                <?php if ($question['type'] == 'name'): ?>
                if (field_<?php echo $question['id']; ?> && !/^[\u4e00-\u9fa5]{2,}$/.test(field_<?php echo $question['id']; ?>.value.trim())) {
                    if(error_<?php echo $question['id']; ?>) error_<?php echo $question['id']; ?>.textContent = '请输入有效的中文姓名。';
                    isValid = false;
                }
                <?php elseif ($question['type'] == 'id_card'): ?>
                if (field_<?php echo $question['id']; ?> && !/^\d{17}(\d|X|x)$/.test(field_<?php echo $question['id']; ?>.value.trim())) {
                     if(error_<?php echo $question['id']; ?>) error_<?php echo $question['id']; ?>.textContent = '请输入有效的18位身份证号码。';
                    isValid = false;
                }
                <?php elseif ($question['type'] == 'phone'): ?>
                if (field_<?php echo $question['id']; ?> && !/^1\d{10}$/.test(field_<?php echo $question['id']; ?>.value.trim())) {
                    if(error_<?php echo $question['id']; ?>) error_<?php echo $question['id']; ?>.textContent = '请输入有效的11位手机号码。';
                    isValid = false;
                }
                <?php endif; ?>

            <?php endforeach; ?>
            return isValid;
        }
    </script>
</head>
<body>
    <div class="container">
        <header>
            <h1><?php echo htmlspecialchars($questionnaire_title); ?></h1>
        </header>

        <?php if (!empty($error_message) && empty($questions) && !isset($success_submission_message)): // 初始加载错误 ?>
            <div class="error-message-box"><?php echo $error_message; ?></div>
        <?php endif; ?>

        <?php if (isset($success_submission_message)): ?>
            <div class="success-message-box"><?php echo $success_submission_message; ?></div>
        <?php endif; ?>


        <?php if (!empty($questions) && !isset($success_submission_message)): ?>
            <form action="index.php?id=<?php echo htmlspecialchars($questionnaire_id); ?>" method="post" onsubmit="return validateForm();">
                <?php if (!empty($error_message)): // 表单提交后的错误信息（包括验证错误和回收份数限制错误） ?>
                    <div class="error-message-box"><?php echo $error_message; ?></div>
                <?php endif; ?>

                <?php foreach ($questions as $question): ?>
                    <div class="question-item">
                        <label for="q_<?php echo $question['id']; ?>"><?php echo htmlspecialchars($question['label']); ?></label>
                        <?php
                        $fieldName = "answers[q_" . $question['id'] . "]";
                        $fieldId = "q_" . $question['id'];
                        $fieldValue = isset($_POST['answers'][$fieldId]) ? htmlspecialchars($_POST['answers'][$fieldId]) : '';
                        ?>
                        <?php if ($question['type'] == 'name' || $question['type'] == 'id_card' || $question['type'] == 'phone' || $question['type'] == 'fill_blank'): ?>
                            <input type="text" id="<?php echo $fieldId; ?>" name="<?php echo $fieldName; ?>" value="<?php echo $fieldValue; ?>" required>
                        <?php elseif ($question['type'] == 'single_choice' && !empty($question['options'])): ?>
                            <select id="<?php echo $fieldId; ?>" name="<?php echo $fieldName; ?>" required>
                                <option value="">请选择...</option>
                                <?php foreach ($question['options'] as $option_value): ?>
                                    <option value="<?php echo htmlspecialchars($option_value); ?>" <?php echo ($fieldValue == $option_value) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($option_value); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        <?php else: ?>
                            <p>未知或配置错误的题型。</p>
                        <?php endif; ?>
                        <div class="form-field-error" id="error_<?php echo $fieldId; ?>">
                            <?php echo isset($form_errors[$fieldId]) ? $form_errors[$fieldId] : ''; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
                <button type="submit" name="submit_survey" class="submit-button">提交问卷</button>
            </form>
        <?php elseif (empty($error_message) && empty($questions) && !isset($success_submission_message)): ?>
             <!-- 问卷存在但无题目，或问卷ID有效但未发布（且后台未设为错误） -->
            <p style="text-align:center;">此问卷当前没有可显示的题目。</p>
        <?php endif; ?>
    </div>

    <footer>
        太仓市学生发展指导中心 版权所有
    </footer>
</body>
</html>
