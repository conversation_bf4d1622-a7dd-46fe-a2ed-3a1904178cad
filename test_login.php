<?php
// 测试登录功能
session_start();
include_once 'config/db_config.php';

echo "测试管理员登录功能\n";
echo "====================\n";

// 测试数据库连接
if ($mysqli->connect_error) {
    echo "数据库连接失败: " . $mysqli->connect_error . "\n";
    exit;
}
echo "数据库连接成功\n";

// 测试admin_users表是否存在
$result = $mysqli->query("SHOW TABLES LIKE 'admin_users'");
if ($result->num_rows == 0) {
    echo "admin_users表不存在\n";
    exit;
}
echo "admin_users表存在\n";

// 查看管理员账户
$result = $mysqli->query("SELECT id, username FROM admin_users");
echo "管理员账户列表:\n";
while ($row = $result->fetch_assoc()) {
    echo "ID: " . $row['id'] . ", 用户名: " . $row['username'] . "\n";
}

// 测试密码验证
$username = 'admin';
$password = 'admin';
$stmt = $mysqli->prepare("SELECT id, username, password FROM admin_users WHERE username = ?");
if ($stmt) {
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 1) {
        $admin = $result->fetch_assoc();
        echo "\n找到用户: " . $admin['username'] . "\n";
        echo "存储的密码哈希: " . $admin['password'] . "\n";
        echo "输入密码的MD5: " . md5($password) . "\n";
        
        if ($admin['password'] === md5($password)) {
            echo "密码验证成功！\n";
        } else {
            echo "密码验证失败！\n";
        }
    } else {
        echo "用户不存在\n";
    }
    $stmt->close();
} else {
    echo "数据库查询失败\n";
}

$mysqli->close();
?>
